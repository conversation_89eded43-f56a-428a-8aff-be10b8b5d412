# SOP 作業員工辨識系統配置文件

# 影片設定
VIDEO_PATH = 'video_sample.mp4'

# YOLO 模型設定
YOLO_MODEL_PATH = 'yolov8n.pt'
YOLO_CONFIDENCE = 0.5

# MediaPipe 設定
MP_DETECTION_CONFIDENCE = 0.5
MP_TRACKING_CONFIDENCE = 0.5
MP_MODEL_COMPLEXITY = 1

# 作業區域設定 (如果預先知道可以設定，否則設為 None 讓用戶手動設定)
# 格式: [x1, y1, x2, y2] 左上角和右下角座標
PREDEFINED_TASK_AREA = None
# 範例: PREDEFINED_TASK_AREA = [300, 200, 600, 500]

# ROI 擴展設定
ROI_PADDING = 20  # 人員ROI區域的邊界擴展像素

# 顯示設定
WINDOW_NAME = 'SOP Worker Detection'
MAIN_WORKER_COLOR = (0, 255, 0)  # 綠色
OTHER_PERSON_COLOR = (0, 0, 255)  # 紅色
TASK_AREA_COLOR = (255, 255, 0)  # 黃色
POSE_COLOR = (255, 0, 255)  # 紫色

# 文字設定
FONT = cv2.FONT_HERSHEY_SIMPLEX
FONT_SCALE = 0.7
FONT_THICKNESS = 2

# 控制鍵設定
QUIT_KEY = ord('q')
RESET_AREA_KEY = ord('r')
PAUSE_KEY = ord(' ')  # 空白鍵暫停

# 常見的冷氣作業區域預設值 (可根據實際情況調整)
COMMON_TASK_AREAS = {
    'indoor_unit': [200, 150, 500, 400],      # 室內機區域
    'outdoor_unit': [100, 100, 400, 350],    # 室外機區域
    'control_panel': [300, 200, 600, 450],   # 控制面板區域
    'full_frame': [50, 50, 750, 550]         # 全畫面 (適用於單人作業)
}

# 偵測優化設定
MIN_PERSON_AREA = 1000  # 最小人員區域面積 (像素)
MAX_DISTANCE_THRESHOLD = 200  # 最大距離閾值 (超過此距離不考慮為主要員工)

# 追蹤設定
TRACKING_HISTORY_LENGTH = 30  # 追蹤歷史長度 (幀數)
WORKER_STABILITY_THRESHOLD = 0.7  # 員工穩定性閾值

# 除錯模式
DEBUG_MODE = False  # 是否顯示除錯資訊
SHOW_ALL_DETECTIONS = True  # 是否顯示所有偵測結果
SHOW_CONFIDENCE = True  # 是否顯示信心度
