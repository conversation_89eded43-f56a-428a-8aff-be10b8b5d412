
import cv2
import mediapipe as mp
import numpy as np
from tensorflow.keras.models import load_model
from sklearn.preprocessing import LabelEncoder
import joblib

model = load_model('sop_model.h5')
label_encoder = joblib.load('label_encoder.pkl')

mp_pose = mp.solutions.pose
pose = mp_pose.Pose()

cap = cv2.VideoCapture('video_sample.mp4')
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = pose.process(rgb)
    if results.pose_landmarks:
        features = []
        for lm in results.pose_landmarks.landmark:
            features += [lm.x, lm.y, lm.z, lm.visibility]
        X = np.array(features).reshape(1, 1, -1)
        y_pred = model.predict(X)
        label = label_encoder.inverse_transform([np.argmax(y_pred)])[0]
        print("辨識動作：", label)
cap.release()
