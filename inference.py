
import cv2
import mediapipe as mp
import numpy as np
from tensorflow.keras.models import load_model
from sklearn.preprocessing import LabelEncoder
import joblib
import json
import os
from ultralytics import YOLO

# 載入模型
model = load_model('sop_model.h5')
label_encoder = joblib.load('label_encoder.pkl')

# 初始化
mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
yolo_model = YOLO('yolov8n.pt')

def load_task_area():
    """載入作業區域設定"""
    config_file = 'task_area_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def find_main_worker(frame, task_area):
    """找出主要作業員工"""
    results = yolo_model.predict(frame, conf=0.5, verbose=False)
    people = []

    if len(results) > 0 and results[0].boxes is not None:
        for box in results[0].boxes:
            if int(box.cls[0]) == 0:  # person class
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                people.append([int(x1), int(y1), int(x2), int(y2)])

    if not people:
        return None

    if task_area is None:
        return people[0]

    # 找距離作業區域最近的人
    task_center = center(task_area)
    closest_person = min(people, key=lambda box: distance(center(box), task_center))
    return closest_person

def extract_person_roi(frame, person_box, padding=20):
    """提取人員ROI"""
    if person_box is None:
        return frame

    h, w = frame.shape[:2]
    x1, y1, x2, y2 = person_box

    x1 = max(0, x1 - padding)
    y1 = max(0, y1 - padding)
    x2 = min(w, x2 + padding)
    y2 = min(h, y2 + padding)

    return frame[y1:y2, x1:x2]

# 載入作業區域設定
task_area = load_task_area()
if task_area:
    print(f"✅ 載入作業區域設定: {task_area}")
    print("🎯 將只分析主要作業員工的動作")
else:
    print("⚠️ 未找到作業區域設定，將分析第一個偵測到的人員")

cap = cv2.VideoCapture('1.mp4')
frame_count = 0
detection_count = 0

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    frame_count += 1

    # 找出主要作業員工
    main_worker_box = find_main_worker(frame, task_area)

    if main_worker_box is not None:
        # 提取主要員工的ROI
        person_roi = extract_person_roi(frame, main_worker_box)

        # 姿勢分析
        rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
        results = pose.process(rgb)

        if results.pose_landmarks:
            features = []
            for lm in results.pose_landmarks.landmark:
                features += [lm.x, lm.y, lm.z, lm.visibility]

            X = np.array(features).reshape(1, 1, -1)
            y_pred = model.predict(X, verbose=0)
            label = label_encoder.inverse_transform([np.argmax(y_pred)])[0]

            print(f"幀 {frame_count}: 辨識動作 - {label}")
            detection_count += 1

cap.release()
print(f"\n📊 推論完成:")
print(f"總幀數: {frame_count}")
print(f"成功辨識: {detection_count}")
print(f"辨識率: {detection_count/frame_count*100:.1f}%")
