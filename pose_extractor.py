
import cv2
import mediapipe as mp
import csv
import json
import os
from ultralytics import YOLO

# 初始化
mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
model = YOLO('yolov8n.pt')

video_path = '1.mp4'
csv_path = 'pose_data.csv'
config_file = 'task_area_config.json'

def load_task_area():
    """載入作業區域設定"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def find_main_worker(frame, task_area):
    """找出主要作業員工"""
    results = model.predict(frame, conf=0.5, verbose=False)
    people = []

    if len(results) > 0 and results[0].boxes is not None:
        for box in results[0].boxes:
            if int(box.cls[0]) == 0:  # person class
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                people.append([int(x1), int(y1), int(x2), int(y2)])

    if not people:
        return None

    if task_area is None:
        return people[0]  # 選第一個人

    # 找距離作業區域最近的人
    task_center = center(task_area)
    closest_person = min(people, key=lambda box: distance(center(box), task_center))
    return closest_person

def extract_person_roi(frame, person_box, padding=20):
    """提取人員ROI"""
    if person_box is None:
        return frame

    h, w = frame.shape[:2]
    x1, y1, x2, y2 = person_box

    x1 = max(0, x1 - padding)
    y1 = max(0, y1 - padding)
    x2 = min(w, x2 + padding)
    y2 = min(h, y2 + padding)

    return frame[y1:y2, x1:x2]

# 載入作業區域設定
task_area = load_task_area()
if task_area:
    print(f"✅ 載入作業區域設定: {task_area}")
    print("🎯 將只分析主要作業員工的姿勢")
else:
    print("⚠️ 未找到作業區域設定，將分析第一個偵測到的人員")
    print("💡 建議先運行 yolo_pose_gui.py 設定作業區域")

cap = cv2.VideoCapture(video_path)
fps = int(cap.get(cv2.CAP_PROP_FPS))

with open(csv_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['frame', 'label'] + [f'{i}_{c}' for i in range(33) for c in ['x', 'y', 'z', 'visibility']])

    frame_id = 0
    processed_frames = 0

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # 找出主要作業員工
        main_worker_box = find_main_worker(frame, task_area)

        if main_worker_box is not None:
            # 提取主要員工的ROI
            person_roi = extract_person_roi(frame, main_worker_box)

            # 對ROI進行姿勢分析
            rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
            results = pose.process(rgb)

            if results.pose_landmarks:
                row = [frame_id, '']  # Label 手動填寫
                for lm in results.pose_landmarks.landmark:
                    row += [lm.x, lm.y, lm.z, lm.visibility]
                writer.writerow(row)
                processed_frames += 1

        frame_id += 1

        # 顯示進度
        if frame_id % 30 == 0:  # 每30幀顯示一次
            print(f"處理進度: {frame_id} 幀, 成功分析: {processed_frames} 幀")

cap.release()
print(f"✅ 姿勢資料已儲存到 {csv_path}")
print(f"📊 總共處理 {frame_id} 幀，成功分析 {processed_frames} 幀")
print(f"📈 成功率: {processed_frames/frame_id*100:.1f}%")
