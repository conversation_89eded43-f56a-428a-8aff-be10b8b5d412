
import cv2
import mediapipe as mp
import csv

mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
video_path = 'video_sample.mp4'
csv_path = 'pose_data.csv'

cap = cv2.VideoCapture(video_path)
fps = int(cap.get(cv2.CAP_PROP_FPS))

with open(csv_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['frame', 'label'] + [f'{i}_{c}' for i in range(33) for c in ['x', 'y', 'z', 'visibility']])

    frame_id = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = pose.process(rgb)
        if results.pose_landmarks:
            row = [frame_id, '']  # Label 手動填寫
            for lm in results.pose_landmarks.landmark:
                row += [lm.x, lm.y, lm.z, lm.visibility]
            writer.writerow(row)
        frame_id += 1

cap.release()
print(f"Pose data saved to {csv_path}")
