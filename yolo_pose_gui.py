
from ultralytics import YOLO
import cv2
import mediapipe as mp
import json
import os

# 初始化
mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
mp_drawing = mp.solutions.drawing_utils
model = YOLO('yolov8n.pt')

# 作業區域設定檔案
CONFIG_FILE = 'task_area_config.json'

def load_task_area():
    """載入已儲存的作業區域設定"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def save_task_area(area):
    """儲存作業區域設定"""
    config = {
        'task_area': area,
        'video_file': '1.mp4',
        'description': '作業區域座標 [x1, y1, x2, y2]'
    }
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"✅ 作業區域已儲存到 {CONFIG_FILE}")

# 載入已儲存的作業區域，如果沒有則設為None
task_area = load_task_area()
click_points = []

if task_area:
    print(f"📁 載入已儲存的作業區域: {task_area}")
else:
    print("🆕 未找到儲存的作業區域，請手動設定")

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def mouse_callback(event, x, y, flags, param):
    """滑鼠點擊設定作業區域"""
    global task_area, click_points
    if event == cv2.EVENT_LBUTTONDOWN and task_area is None:
        click_points.append((x, y))
        print(f"點擊點 {len(click_points)}: ({x}, {y})")
        if len(click_points) == 2:
            x1, y1 = click_points[0]
            x2, y2 = click_points[1]
            task_area = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
            print(f"作業區域設定完成: {task_area}")
            # 自動儲存設定
            save_task_area(task_area)

def find_main_worker(frame):
    """找出主要作業員工"""
    # YOLO偵測所有人
    results = model.predict(frame, conf=0.5, verbose=False)
    people = []

    if len(results) > 0 and results[0].boxes is not None:
        for box in results[0].boxes:
            if int(box.cls[0]) == 0:  # person class
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                people.append([int(x1), int(y1), int(x2), int(y2)])

    if not people:
        return None, []

    # 如果沒設定作業區域，選信心度最高的（第一個）
    if task_area is None:
        return people[0], people

    # 找距離作業區域最近的人
    task_center = center(task_area)
    closest_person = min(people, key=lambda box: distance(center(box), task_center))
    return closest_person, people

# 主程式
cap = cv2.VideoCapture('1.mp4')

# 檢查影片是否成功開啟
if not cap.isOpened():
    print("❌ 錯誤：無法開啟影片檔案 '1.mp4'")
    print("請確認檔案存在且格式正確")
    exit()

# 獲取影片資訊
fps = cap.get(cv2.CAP_PROP_FPS)
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

print(f"✅ 影片載入成功：{width}x{height}, {fps:.1f}fps, {total_frames}幀")

cv2.namedWindow('YOLO + Pose GUI', cv2.WINDOW_NORMAL)
cv2.resizeWindow('YOLO + Pose GUI', 800, 600)
cv2.setMouseCallback('YOLO + Pose GUI', mouse_callback)

print("=== 智能作業員工辨識系統 ===")
if task_area:
    print("✅ 已載入儲存的作業區域")
else:
    print("1. 請在視窗中點擊兩個點來設定作業區域")
print("2. 系統會自動選擇最接近作業區域的員工進行姿勢分析")
print("3. 綠色框 = 主要作業員工，紅色框 = 其他人員")
print("🎮 控制鍵：")
print("   'q' = 退出")
print("   'r' = 重設作業區域")
print("   's' = 手動儲存當前設定")
print("🖥️ 視窗應該已經開啟，請檢查工作列或其他螢幕")

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # 如果還在設定作業區域
    if task_area is None and len(click_points) < 2:
        display_frame = frame.copy()
        cv2.putText(display_frame, "Click 2 points to set task area (or skip)",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 顯示已點擊的點
        for i, point in enumerate(click_points):
            cv2.circle(display_frame, point, 5, (0, 255, 0), -1)
            cv2.putText(display_frame, f"P{i+1}", (point[0]+10, point[1]-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        cv2.imshow('YOLO + Pose GUI', display_frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
        continue

    # 找出主要作業員工
    main_worker_box, all_people = find_main_worker(frame)

    # 複製原始影像用於顯示
    display_frame = frame.copy()

    # 繪製作業區域
    if task_area:
        x1, y1, x2, y2 = task_area
        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
        cv2.putText(display_frame, "Task Area", (x1, y1-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

    # 繪製所有偵測到的人員
    for person_box in all_people:
        x1, y1, x2, y2 = person_box

        # 判斷是否為主要員工
        is_main = (main_worker_box is not None and person_box == main_worker_box)
        color = (0, 255, 0) if is_main else (0, 0, 255)  # 綠色=主要員工，紅色=其他人
        thickness = 3 if is_main else 2

        cv2.rectangle(display_frame, (x1, y1), (x2, y2), color, thickness)
        label = "Main Worker" if is_main else "Person"
        cv2.putText(display_frame, label, (x1, y1-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # 如果找到主要員工，進行姿勢偵測
    if main_worker_box is not None:
        # 提取主要員工的ROI (加一點padding)
        x1, y1, x2, y2 = main_worker_box
        padding = 20
        h, w = frame.shape[:2]
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(w, x2 + padding)
        y2 = min(h, y2 + padding)

        person_roi = frame[y1:y2, x1:x2]

        # MediaPipe 姿勢偵測
        rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
        results_pose = pose.process(rgb_roi)

        if results_pose.pose_landmarks:
            # 將ROI中的關鍵點座標轉換回原圖座標並繪製
            for landmark in results_pose.pose_landmarks.landmark:
                x = int(landmark.x * person_roi.shape[1] + x1)
                y = int(landmark.y * person_roi.shape[0] + y1)
                cv2.circle(display_frame, (x, y), 3, (255, 0, 255), -1)

            # 繪製骨架連接
            connections = mp_pose.POSE_CONNECTIONS
            for connection in connections:
                start_idx, end_idx = connection
                start_landmark = results_pose.pose_landmarks.landmark[start_idx]
                end_landmark = results_pose.pose_landmarks.landmark[end_idx]

                start_x = int(start_landmark.x * person_roi.shape[1] + x1)
                start_y = int(start_landmark.y * person_roi.shape[0] + y1)
                end_x = int(end_landmark.x * person_roi.shape[1] + x1)
                end_y = int(end_landmark.y * person_roi.shape[0] + y1)

                cv2.line(display_frame, (start_x, start_y), (end_x, end_y), (255, 0, 255), 2)

    # 顯示統計資訊
    info_text = f"People detected: {len(all_people)}"
    cv2.putText(display_frame, info_text, (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    cv2.imshow('YOLO + Pose GUI', display_frame)

    # 鍵盤控制
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('r'):
        # 重設作業區域
        task_area = None
        click_points = []
        print("🔄 重設作業區域，請重新點擊兩個點")
    elif key == ord('s') and task_area:
        # 手動儲存
        save_task_area(task_area)
        print("💾 設定已手動儲存")

cap.release()
cv2.destroyAllWindows()
