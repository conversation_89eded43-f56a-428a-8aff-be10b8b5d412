
from ultralytics import YOLO
import cv2
import mediapipe as mp

mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
model = YOLO('yolov8n.pt')

cap = cv2.VideoCapture('video_sample.mp4')
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # YOLO 偵測
    results = model.predict(frame, conf=0.5)
    yolo_frame = results[0].plot()

    # MediaPipe Pose 偵測
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results_pose = pose.process(rgb)
    if results_pose.pose_landmarks:
        mp.solutions.drawing_utils.draw_landmarks(
            yolo_frame, results_pose.pose_landmarks, mp_pose.POSE_CONNECTIONS)

    cv2.imshow('YOLO + Pose GUI', yolo_frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break
cap.release()
cv2.destroyAllWindows()
