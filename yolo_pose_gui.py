
from ultralytics import YOLO
import cv2
import mediapipe as mp

class WorkerDetector:
    def __init__(self, task_area=None):
        """
        初始化工作人員偵測器

        Args:
            task_area: [x1, y1, x2, y2] 作業區域座標，如果為None則會要求用戶點擊設定
        """
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=False,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        self.model = YOLO('yolov8n.pt')

        # 作業區域設定
        self.task_area = task_area
        self.setting_area = task_area is None
        self.click_points = []

        # 追蹤相關
        self.main_worker_id = None
        self.worker_history = {}  # 記錄每個worker在作業區域的出現次數

    def set_task_area_callback(self, event, x, y, flags, param):
        """滑鼠回調函數，用於設定作業區域"""
        if event == cv2.EVENT_LBUTTONDOWN and self.setting_area:
            self.click_points.append((x, y))
            if len(self.click_points) == 2:
                # 計算矩形區域
                x1, y1 = self.click_points[0]
                x2, y2 = self.click_points[1]
                self.task_area = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
                self.setting_area = False
                print(f"作業區域已設定: {self.task_area}")

    def center(self, box):
        """計算bounding box的中心點"""
        x1, y1, x2, y2 = box
        return ((x1 + x2) / 2, (y1 + y2) / 2)

    def distance(self, p1, p2):
        """計算兩點間距離"""
        return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

    def is_in_task_area(self, person_center):
        """判斷人員是否在作業區域內"""
        if self.task_area is None:
            return False
        x1, y1, x2, y2 = self.task_area
        px, py = person_center
        return x1 <= px <= x2 and y1 <= py <= y2

    def find_main_worker(self, frame):
        """
        找出主要作業員工

        Returns:
            main_worker_box: 主要員工的bounding box [x1, y1, x2, y2]
            all_people: 所有偵測到的人員列表
        """
        # YOLO 偵測
        results = self.model.predict(frame, conf=0.5, verbose=False)

        # 提取人員偵測結果
        people = []
        if len(results) > 0 and results[0].boxes is not None:
            for box in results[0].boxes:
                # 檢查是否為人員 (class 0 = person)
                if int(box.cls[0]) == 0:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    people.append({
                        'box': [int(x1), int(y1), int(x2), int(y2)],
                        'confidence': confidence,
                        'center': self.center([x1, y1, x2, y2])
                    })

        if not people:
            return None, []

        # 如果沒有設定作業區域，返回信心度最高的人
        if self.task_area is None:
            main_worker = max(people, key=lambda p: p['confidence'])
            return main_worker['box'], people

        # 方法A: 找出距離作業區域最近的人
        task_center = self.center(self.task_area)

        # 優先選擇在作業區域內的人
        people_in_area = [p for p in people if self.is_in_task_area(p['center'])]

        if people_in_area:
            # 如果有人在作業區域內，選擇信心度最高的
            main_worker = max(people_in_area, key=lambda p: p['confidence'])
        else:
            # 如果沒有人在作業區域內，選擇距離最近的
            main_worker = min(people, key=lambda p: self.distance(p['center'], task_center))

        return main_worker['box'], people

    def extract_person_roi(self, frame, person_box, padding=20):
        """
        提取人員的ROI區域，加上適當的padding

        Args:
            frame: 原始影像
            person_box: 人員的bounding box [x1, y1, x2, y2]
            padding: 邊界擴展像素

        Returns:
            person_roi: 裁切後的人員區域
            roi_coords: ROI在原圖中的座標 [x1, y1, x2, y2]
        """
        h, w = frame.shape[:2]
        x1, y1, x2, y2 = person_box

        # 加上padding並確保不超出邊界
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(w, x2 + padding)
        y2 = min(h, y2 + padding)

        person_roi = frame[y1:y2, x1:x2]
        return person_roi, [x1, y1, x2, y2]

    def process_frame(self, frame):
        """
        處理單一影格

        Returns:
            processed_frame: 處理後的影像
            pose_landmarks: MediaPipe姿勢關鍵點
            main_worker_info: 主要員工資訊
        """
        # 如果正在設定作業區域，顯示提示
        if self.setting_area:
            display_frame = frame.copy()
            cv2.putText(display_frame, "Click 2 points to set task area",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            # 顯示已點擊的點
            for i, point in enumerate(self.click_points):
                cv2.circle(display_frame, point, 5, (0, 255, 0), -1)
                cv2.putText(display_frame, f"P{i+1}",
                           (point[0]+10, point[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            return display_frame, None, None

        # 找出主要作業員工
        main_worker_box, all_people = self.find_main_worker(frame)

        # 複製原始影像用於顯示
        display_frame = frame.copy()

        # 繪製作業區域
        if self.task_area:
            x1, y1, x2, y2 = self.task_area
            cv2.rectangle(display_frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
            cv2.putText(display_frame, "Task Area", (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

        # 繪製所有偵測到的人員
        for i, person in enumerate(all_people):
            box = person['box']
            conf = person['confidence']
            x1, y1, x2, y2 = box

            # 判斷是否為主要員工
            is_main = (main_worker_box is not None and box == main_worker_box)
            color = (0, 255, 0) if is_main else (0, 0, 255)  # 綠色=主要員工，紅色=其他人
            thickness = 3 if is_main else 2

            cv2.rectangle(display_frame, (x1, y1), (x2, y2), color, thickness)
            label = f"Main Worker {conf:.2f}" if is_main else f"Person {conf:.2f}"
            cv2.putText(display_frame, label, (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # 如果找到主要員工，進行姿勢偵測
        pose_landmarks = None
        if main_worker_box is not None:
            # 提取主要員工的ROI
            person_roi, roi_coords = self.extract_person_roi(frame, main_worker_box)

            # MediaPipe 姿勢偵測
            rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
            results_pose = self.pose.process(rgb_roi)

            if results_pose.pose_landmarks:
                pose_landmarks = results_pose.pose_landmarks

                # 將ROI中的關鍵點座標轉換回原圖座標
                roi_x1, roi_y1 = roi_coords[0], roi_coords[1]

                # 在原圖上繪製姿勢關鍵點
                for landmark in pose_landmarks.landmark:
                    # 轉換座標
                    x = int(landmark.x * person_roi.shape[1] + roi_x1)
                    y = int(landmark.y * person_roi.shape[0] + roi_y1)
                    cv2.circle(display_frame, (x, y), 3, (255, 0, 255), -1)

                # 繪製骨架連接
                connections = self.mp_pose.POSE_CONNECTIONS
                for connection in connections:
                    start_idx, end_idx = connection
                    start_landmark = pose_landmarks.landmark[start_idx]
                    end_landmark = pose_landmarks.landmark[end_idx]

                    start_x = int(start_landmark.x * person_roi.shape[1] + roi_x1)
                    start_y = int(start_landmark.y * person_roi.shape[0] + roi_y1)
                    end_x = int(end_landmark.x * person_roi.shape[1] + roi_x1)
                    end_y = int(end_landmark.y * person_roi.shape[0] + roi_y1)

                    cv2.line(display_frame, (start_x, start_y), (end_x, end_y), (255, 0, 255), 2)

        # 顯示統計資訊
        info_text = f"People detected: {len(all_people)}"
        cv2.putText(display_frame, info_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        main_worker_info = {
            'box': main_worker_box,
            'total_people': len(all_people),
            'has_pose': pose_landmarks is not None
        }

        return display_frame, pose_landmarks, main_worker_info

def main():
    # 初始化偵測器 (不設定作業區域，讓用戶手動設定)
    detector = WorkerDetector(task_area=None)

    # 也可以預設作業區域，例如：
    # detector = WorkerDetector(task_area=[300, 200, 600, 500])

    cap = cv2.VideoCapture('video_sample.mp4')

    # 設定滑鼠回調
    cv2.namedWindow('SOP Worker Detection')
    cv2.setMouseCallback('SOP Worker Detection', detector.set_task_area_callback)

    print("=== SOP 作業員工自動辨識系統 ===")
    print("功能說明：")
    print("1. 如果未設定作業區域，請在視窗中點擊兩個點來設定")
    print("2. 系統會自動選擇最接近作業區域的員工進行姿勢分析")
    print("3. 綠色框 = 主要作業員工，紅色框 = 其他人員")
    print("4. 按 'q' 退出，按 'r' 重設作業區域")
    print("=" * 50)

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # 處理影格
        processed_frame, _, _ = detector.process_frame(frame)

        # 顯示結果
        cv2.imshow('SOP Worker Detection', processed_frame)

        # 鍵盤控制
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('r'):
            # 重設作業區域
            detector.task_area = None
            detector.setting_area = True
            detector.click_points = []
            print("重設作業區域，請重新點擊兩個點")

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
