"""
智能作業員工偵測系統
解決多人環境下的主要作業員自動識別問題

功能特色：
1. 自動偵測並鎖定主要作業員工
2. 支援作業區域設定和距離判斷
3. 整合 YOLOv8 人員偵測和 MediaPipe 姿勢分析
4. 可視化顯示和即時追蹤
"""

from ultralytics import YOLO
import cv2
import mediapipe as mp
from config import *

class SmartWorkerDetector:
    def __init__(self, video_path=None, task_area=None):
        """
        初始化智能作業員工偵測器
        
        Args:
            video_path: 影片路徑
            task_area: 預設作業區域 [x1, y1, x2, y2]
        """
        # MediaPipe 初始化
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=MP_MODEL_COMPLEXITY,
            enable_segmentation=False,
            min_detection_confidence=MP_DETECTION_CONFIDENCE,
            min_tracking_confidence=MP_TRACKING_CONFIDENCE
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # YOLO 模型初始化
        self.model = YOLO(YOLO_MODEL_PATH)
        
        # 影片設定
        self.video_path = video_path or VIDEO_PATH
        self.cap = None
        
        # 作業區域設定
        self.task_area = task_area or PREDEFINED_TASK_AREA
        self.setting_area = self.task_area is None
        self.click_points = []
        
        # 追蹤和統計
        self.frame_count = 0
        self.worker_stats = {
            'total_frames': 0,
            'detected_frames': 0,
            'pose_detected_frames': 0
        }
        
        # 控制狀態
        self.paused = False
        
    def set_task_area_callback(self, event, x, y, flags, param):
        """滑鼠回調函數，用於設定作業區域"""
        if event == cv2.EVENT_LBUTTONDOWN and self.setting_area:
            self.click_points.append((x, y))
            print(f"點擊點 {len(self.click_points)}: ({x}, {y})")
            
            if len(self.click_points) == 2:
                # 計算矩形區域
                x1, y1 = self.click_points[0]
                x2, y2 = self.click_points[1]
                self.task_area = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
                self.setting_area = False
                print(f"✅ 作業區域已設定: {self.task_area}")
                print("系統將自動選擇最接近此區域的員工進行分析")
    
    def center(self, box):
        """計算bounding box的中心點"""
        x1, y1, x2, y2 = box
        return ((x1 + x2) / 2, (y1 + y2) / 2)
    
    def distance(self, p1, p2):
        """計算兩點間距離"""
        return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5
    
    def is_in_task_area(self, person_center):
        """判斷人員是否在作業區域內"""
        if self.task_area is None:
            return False
        x1, y1, x2, y2 = self.task_area
        px, py = person_center
        return x1 <= px <= x2 and y1 <= py <= y2
    
    def calculate_person_area(self, box):
        """計算人員bounding box面積"""
        x1, y1, x2, y2 = box
        return (x2 - x1) * (y2 - y1)
    
    def find_main_worker(self, frame):
        """
        智能找出主要作業員工
        
        策略：
        1. 優先選擇在作業區域內的人員
        2. 如果沒有人在區域內，選擇距離最近的
        3. 考慮人員大小和信心度
        
        Returns:
            main_worker: 主要員工資訊字典
            all_people: 所有偵測到的人員列表
        """
        # YOLO 偵測
        results = self.model.predict(frame, conf=YOLO_CONFIDENCE, verbose=False)
        
        # 提取人員偵測結果
        people = []
        if len(results) > 0 and results[0].boxes is not None:
            for i, box in enumerate(results[0].boxes):
                # 檢查是否為人員 (class 0 = person)
                if int(box.cls[0]) == 0:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    box_coords = [int(x1), int(y1), int(x2), int(y2)]
                    center = self.center(box_coords)
                    area = self.calculate_person_area(box_coords)
                    
                    # 過濾太小的偵測結果
                    if area >= MIN_PERSON_AREA:
                        people.append({
                            'id': i,
                            'box': box_coords,
                            'confidence': confidence,
                            'center': center,
                            'area': area,
                            'in_task_area': self.is_in_task_area(center)
                        })
        
        if not people:
            return None, []
        
        # 如果沒有設定作業區域，選擇最大且信心度最高的人
        if self.task_area is None:
            main_worker = max(people, key=lambda p: p['confidence'] * p['area'])
            return main_worker, people
        
        # 智能選擇策略
        task_center = self.center(self.task_area)
        
        # 第一優先：在作業區域內的人員
        people_in_area = [p for p in people if p['in_task_area']]
        
        if people_in_area:
            # 在區域內的人員中，選擇信心度和面積綜合最高的
            main_worker = max(people_in_area, 
                            key=lambda p: p['confidence'] * (p['area'] / 10000))
        else:
            # 第二優先：距離作業區域最近的人員
            for person in people:
                person['distance_to_task'] = self.distance(person['center'], task_center)
            
            # 過濾距離太遠的人員
            nearby_people = [p for p in people if p['distance_to_task'] <= MAX_DISTANCE_THRESHOLD]
            
            if nearby_people:
                # 選擇距離最近且信心度較高的
                main_worker = min(nearby_people, 
                                key=lambda p: p['distance_to_task'] / (p['confidence'] + 0.1))
            else:
                # 如果都太遠，選擇信心度最高的
                main_worker = max(people, key=lambda p: p['confidence'])
        
        return main_worker, people
    
    def extract_person_roi(self, frame, person_box, padding=ROI_PADDING):
        """提取人員的ROI區域，加上適當的padding"""
        h, w = frame.shape[:2]
        x1, y1, x2, y2 = person_box
        
        # 加上padding並確保不超出邊界
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(w, x2 + padding)
        y2 = min(h, y2 + padding)
        
        person_roi = frame[y1:y2, x1:x2]
        return person_roi, [x1, y1, x2, y2]
    
    def draw_pose_landmarks(self, frame, pose_landmarks, roi_coords):
        """在原圖上繪製姿勢關鍵點和骨架"""
        if not pose_landmarks:
            return
        
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_coords
        roi_width = roi_x2 - roi_x1
        roi_height = roi_y2 - roi_y1
        
        # 繪製關鍵點
        for landmark in pose_landmarks.landmark:
            x = int(landmark.x * roi_width + roi_x1)
            y = int(landmark.y * roi_height + roi_y1)
            cv2.circle(frame, (x, y), 3, POSE_COLOR, -1)
        
        # 繪製骨架連接
        connections = self.mp_pose.POSE_CONNECTIONS
        for connection in connections:
            start_idx, end_idx = connection
            start_landmark = pose_landmarks.landmark[start_idx]
            end_landmark = pose_landmarks.landmark[end_idx]
            
            start_x = int(start_landmark.x * roi_width + roi_x1)
            start_y = int(start_landmark.y * roi_height + roi_y1)
            end_x = int(end_landmark.x * roi_width + roi_x1)
            end_y = int(end_landmark.y * roi_height + roi_y1)
            
            cv2.line(frame, (start_x, start_y), (end_x, end_y), POSE_COLOR, 2)
    
    def draw_ui_elements(self, frame, main_worker, all_people):
        """繪製UI元素：框框、標籤、統計資訊等"""
        # 繪製作業區域
        if self.task_area:
            x1, y1, x2, y2 = self.task_area
            cv2.rectangle(frame, (x1, y1), (x2, y2), TASK_AREA_COLOR, 2)
            cv2.putText(frame, "Task Area", (x1, y1-10), 
                       FONT, FONT_SCALE, TASK_AREA_COLOR, FONT_THICKNESS)
        
        # 繪製所有偵測到的人員
        for person in all_people:
            box = person['box']
            conf = person['confidence']
            x1, y1, x2, y2 = box
            
            # 判斷是否為主要員工
            is_main = (main_worker is not None and person['id'] == main_worker['id'])
            color = MAIN_WORKER_COLOR if is_main else OTHER_PERSON_COLOR
            thickness = 3 if is_main else 2
            
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
            
            # 標籤文字
            if is_main:
                label = f"Main Worker"
                if SHOW_CONFIDENCE:
                    label += f" {conf:.2f}"
                if person.get('in_task_area', False):
                    label += " [IN AREA]"
            else:
                label = f"Person"
                if SHOW_CONFIDENCE:
                    label += f" {conf:.2f}"
            
            cv2.putText(frame, label, (x1, y1-10), 
                       FONT, FONT_SCALE-0.1, color, FONT_THICKNESS)
        
        # 顯示統計資訊
        stats_y = 30
        stats = [
            f"Frame: {self.frame_count}",
            f"People: {len(all_people)}",
            f"Detection Rate: {self.worker_stats['detected_frames']}/{self.worker_stats['total_frames']}"
        ]
        
        if main_worker and main_worker.get('in_task_area', False):
            stats.append("✓ Worker in Task Area")
        
        for i, stat in enumerate(stats):
            cv2.putText(frame, stat, (10, stats_y + i*25), 
                       FONT, FONT_SCALE-0.2, (255, 255, 255), FONT_THICKNESS-1)
        
        # 暫停提示
        if self.paused:
            cv2.putText(frame, "PAUSED - Press SPACE to continue", 
                       (frame.shape[1]//2-150, frame.shape[0]//2), 
                       FONT, FONT_SCALE, (0, 255, 255), FONT_THICKNESS)
    
    def process_frame(self, frame):
        """處理單一影格的主要邏輯"""
        self.frame_count += 1
        self.worker_stats['total_frames'] += 1
        
        # 如果正在設定作業區域
        if self.setting_area:
            display_frame = frame.copy()
            cv2.putText(display_frame, "Click 2 points to set task area", 
                       (10, 30), FONT, FONT_SCALE, (0, 255, 0), FONT_THICKNESS)
            
            # 顯示已點擊的點
            for i, point in enumerate(self.click_points):
                cv2.circle(display_frame, point, 5, (0, 255, 0), -1)
                cv2.putText(display_frame, f"P{i+1}", 
                           (point[0]+10, point[1]-10), FONT, 0.5, (0, 255, 0), 1)
            
            return display_frame, None
        
        # 找出主要作業員工
        main_worker, all_people = self.find_main_worker(frame)
        
        # 複製原始影像用於顯示
        display_frame = frame.copy()
        
        # 姿勢偵測
        pose_landmarks = None
        if main_worker is not None:
            self.worker_stats['detected_frames'] += 1
            
            # 提取主要員工的ROI
            person_roi, roi_coords = self.extract_person_roi(frame, main_worker['box'])
            
            # MediaPipe 姿勢偵測
            rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
            results_pose = self.pose.process(rgb_roi)
            
            if results_pose.pose_landmarks:
                pose_landmarks = results_pose.pose_landmarks
                self.worker_stats['pose_detected_frames'] += 1
                
                # 在原圖上繪製姿勢關鍵點
                self.draw_pose_landmarks(display_frame, pose_landmarks, roi_coords)
        
        # 繪製UI元素
        self.draw_ui_elements(display_frame, main_worker, all_people)
        
        return display_frame, pose_landmarks
    
    def run(self):
        """運行主程式"""
        self.cap = cv2.VideoCapture(self.video_path)
        
        if not self.cap.isOpened():
            print(f"❌ 無法開啟影片: {self.video_path}")
            return
        
        # 設定視窗和滑鼠回調
        cv2.namedWindow(WINDOW_NAME)
        cv2.setMouseCallback(WINDOW_NAME, self.set_task_area_callback)
        
        print("=== 智能作業員工偵測系統 ===")
        print("🎯 功能：自動識別主要作業員工並進行姿勢分析")
        print("📋 操作說明：")
        if self.setting_area:
            print("  1. 請在視窗中點擊兩個點來設定作業區域")
        print("  2. 系統會自動選擇最接近作業區域的員工")
        print("  3. 綠色框 = 主要作業員工，紅色框 = 其他人員")
        print("🎮 控制鍵：")
        print("  - 'q': 退出程式")
        print("  - 'r': 重設作業區域")
        print("  - 空白鍵: 暫停/繼續")
        print("=" * 50)
        
        while self.cap.isOpened():
            if not self.paused:
                ret, frame = self.cap.read()
                if not ret:
                    print("📹 影片播放完畢")
                    break
                
                # 處理影格
                processed_frame, pose_landmarks = self.process_frame(frame)
            else:
                # 暫停時繼續顯示當前畫面
                processed_frame = self.current_frame if hasattr(self, 'current_frame') else frame
            
            # 儲存當前畫面用於暫停顯示
            self.current_frame = processed_frame
            
            # 顯示結果
            cv2.imshow(WINDOW_NAME, processed_frame)
            
            # 鍵盤控制
            key = cv2.waitKey(1) & 0xFF
            if key == QUIT_KEY:
                break
            elif key == RESET_AREA_KEY:
                self.reset_task_area()
            elif key == PAUSE_KEY:
                self.paused = not self.paused
                print(f"{'⏸️ 暫停' if self.paused else '▶️ 繼續'}")
        
        # 顯示最終統計
        self.show_final_stats()
        
        self.cap.release()
        cv2.destroyAllWindows()
    
    def reset_task_area(self):
        """重設作業區域"""
        self.task_area = None
        self.setting_area = True
        self.click_points = []
        print("🔄 重設作業區域，請重新點擊兩個點")
    
    def show_final_stats(self):
        """顯示最終統計資訊"""
        total = self.worker_stats['total_frames']
        detected = self.worker_stats['detected_frames']
        pose_detected = self.worker_stats['pose_detected_frames']
        
        print("\n📊 最終統計：")
        print(f"  總影格數: {total}")
        print(f"  偵測到人員: {detected} ({detected/total*100:.1f}%)")
        print(f"  成功姿勢分析: {pose_detected} ({pose_detected/total*100:.1f}%)")
        print("=" * 50)

def main():
    # 可以在這裡設定預設的作業區域
    # detector = SmartWorkerDetector(task_area=COMMON_TASK_AREAS['indoor_unit'])
    detector = SmartWorkerDetector()
    detector.run()

if __name__ == "__main__":
    main()
